"""
CogBridges Search - 数据库模型
使用SQLAlchemy定义数据库表结构，对应现有的JSON数据结构
"""

from sqlalchemy import Column, String, Integer, Float, Text, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional

Base = declarative_base()


class SearchSession(Base):
    """搜索会话表 - 对应完整会话数据"""
    __tablename__ = 'search_sessions'
    
    id = Column(String(50), primary_key=True)  # session_id
    query = Column(Text, nullable=False)  # 搜索查询
    timestamp = Column(DateTime, default=func.now())  # 创建时间
    search_type = Column(String(20), default='reddit')  # 搜索类型
    max_results = Column(Integer, default=5)  # 最大结果数
    site_filter = Column(String(100), default='site:reddit.com')  # 站点过滤
    
    # 搜索结果统计
    google_results_count = Column(Integer, default=0)
    reddit_posts_count = Column(Integer, default=0)
    reddit_comments_count = Column(Integer, default=0)
    
    # 状态信息
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # JSON备份数据
    raw_data = Column(JSON, nullable=True)  # 完整的原始数据
    
    # 关联关系
    google_results = relationship("GoogleSearchResult", back_populates="session", cascade="all, delete-orphan")
    reddit_posts = relationship("RedditPost", back_populates="session", cascade="all, delete-orphan")
    reddit_comments = relationship("RedditComment", back_populates="session", cascade="all, delete-orphan")
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "query": self.query,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "search_type": self.search_type,
            "max_results": self.max_results,
            "site_filter": self.site_filter,
            "google_results_count": self.google_results_count,
            "reddit_posts_count": self.reddit_posts_count,
            "reddit_comments_count": self.reddit_comments_count,
            "success": self.success,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class GoogleSearchResult(Base):
    """Google搜索结果表"""
    __tablename__ = 'google_search_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    
    title = Column(Text, nullable=False)
    url = Column(Text, nullable=False)
    snippet = Column(Text, nullable=True)
    display_url = Column(Text, nullable=True)
    rank = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="google_results")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "title": self.title,
            "url": self.url,
            "snippet": self.snippet,
            "display_url": self.display_url,
            "rank": self.rank,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class RedditPost(Base):
    """Reddit帖子表"""
    __tablename__ = 'reddit_posts'
    
    id = Column(String(20), primary_key=True)  # Reddit post ID
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    
    title = Column(Text, nullable=False)
    selftext = Column(Text, nullable=True)
    author = Column(String(100), nullable=True)
    score = Column(Integer, default=0)
    num_comments = Column(Integer, default=0)
    created_utc = Column(Float, nullable=False)
    subreddit = Column(String(100), nullable=True)
    permalink = Column(Text, nullable=True)
    url = Column(Text, nullable=True)
    
    # 分析结果
    motivation_analysis = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="reddit_posts")
    comments = relationship("RedditComment", back_populates="post", cascade="all, delete-orphan")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "title": self.title,
            "selftext": self.selftext,
            "author": self.author,
            "score": self.score,
            "num_comments": self.num_comments,
            "created_utc": self.created_utc,
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "url": self.url,
            "motivation_analysis": self.motivation_analysis,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class RedditComment(Base):
    """Reddit评论表"""
    __tablename__ = 'reddit_comments'
    
    id = Column(String(20), primary_key=True)  # Reddit comment ID
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    post_id = Column(String(20), ForeignKey('reddit_posts.id'), nullable=True)
    
    body = Column(Text, nullable=False)
    author = Column(String(100), nullable=True)
    score = Column(Integer, default=0)
    created_utc = Column(Float, nullable=False)
    parent_id = Column(String(20), nullable=True)
    subreddit = Column(String(100), nullable=True)
    permalink = Column(Text, nullable=True)
    
    # 分析结果
    motivation_analysis = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="reddit_comments")
    post = relationship("RedditPost", back_populates="comments")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "post_id": self.post_id,
            "body": self.body,
            "author": self.author,
            "score": self.score,
            "created_utc": self.created_utc,
            "parent_id": self.parent_id,
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "motivation_analysis": self.motivation_analysis,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class UserHistory(Base):
    """用户历史数据表"""
    __tablename__ = 'user_histories'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False)
    username = Column(String(100), nullable=False)
    
    # 用户统计信息
    total_comments = Column(Integer, default=0)
    total_posts = Column(Integer, default=0)
    account_created_utc = Column(Float, nullable=True)
    
    # 历史数据（JSON格式存储）
    comments_data = Column(JSON, nullable=True)
    posts_data = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=func.now())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "username": self.username,
            "total_comments": self.total_comments,
            "total_posts": self.total_posts,
            "account_created_utc": self.account_created_utc,
            "comments_data": self.comments_data,
            "posts_data": self.posts_data,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
