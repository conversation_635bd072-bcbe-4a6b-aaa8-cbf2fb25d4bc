#!/usr/bin/env python3
"""
CogBridges Search - 数据库集成测试
测试数据库服务的基本功能
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# 设置测试环境变量
os.environ["ENABLE_DATABASE"] = "False"  # 默认关闭，避免需要真实数据库
os.environ["ENABLE_JSON_BACKUP"] = "True"
os.environ["TEST_MODE"] = "True"

from config import config
from services.data_service import DataService
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from utils.logger_utils import get_logger


def test_data_service_basic():
    """测试数据服务基本功能"""
    logger = get_logger(__name__)
    
    try:
        logger.info("🧪 测试数据服务基本功能...")
        
        # 创建数据服务
        data_service = DataService()
        logger.info("✅ 数据服务创建成功")
        
        # 测试生成会话ID
        test_query = "python programming database"
        session_id = data_service.generate_session_id(test_query)
        logger.info(f"✅ 会话ID生成成功: {session_id}")
        
        # 创建测试搜索结果
        search_query = SearchQuery(
            query=test_query,
            timestamp=datetime.now(),
            search_type="reddit",
            max_results=3
        )
        
        search_result = SearchResult(
            query=search_query,
            success=True
        )
        
        # 添加Google搜索结果
        google_results = [
            GoogleSearchResult(
                title="Python Database Programming",
                url="https://example.com/python-db",
                snippet="Learn Python database programming...",
                display_url="example.com",
                rank=1
            ),
            GoogleSearchResult(
                title="SQLAlchemy Tutorial",
                url="https://example.com/sqlalchemy",
                snippet="Complete SQLAlchemy tutorial...",
                display_url="example.com",
                rank=2
            )
        ]
        search_result.google_results = google_results
        
        # 创建测试Reddit数据
        reddit_data = {
            "reddit_posts": [
                {
                    "id": "test_post_1",
                    "title": "Best Python database libraries?",
                    "selftext": "I'm looking for recommendations...",
                    "author": "python_learner",
                    "score": 150,
                    "num_comments": 25,
                    "created_utc": datetime.now().timestamp(),
                    "subreddit": "Python",
                    "permalink": "/r/Python/comments/test_post_1/",
                    "url": "https://reddit.com/r/Python/comments/test_post_1/"
                }
            ],
            "reddit_comments": [
                {
                    "id": "test_comment_1",
                    "body": "I recommend SQLAlchemy for ORM...",
                    "author": "db_expert",
                    "score": 75,
                    "created_utc": datetime.now().timestamp(),
                    "parent_id": "test_post_1",
                    "subreddit": "Python",
                    "permalink": "/r/Python/comments/test_post_1/test_comment_1/"
                }
            ]
        }
        
        # 构建完整会话数据
        complete_session_data = {
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "search_result": search_result.to_dict(),
            "reddit_data": reddit_data,
            "statistics": {
                "google_results_count": len(google_results),
                "reddit_posts_count": len(reddit_data["reddit_posts"]),
                "reddit_comments_count": len(reddit_data["reddit_comments"])
            }
        }
        
        # 保存完整会话数据
        logger.info("💾 保存完整会话数据...")
        filepath = data_service.save_complete_session(session_id, complete_session_data)
        logger.info(f"✅ 会话数据保存成功: {filepath}")
        
        # 加载会话数据
        logger.info("📂 加载会话数据...")
        loaded_data = data_service.load_session_data(session_id)
        if loaded_data:
            logger.info("✅ 会话数据加载成功")
            logger.info(f"   查询: {loaded_data.get('search_result', {}).get('query', {}).get('query', 'N/A')}")
            logger.info(f"   Google结果数: {loaded_data.get('statistics', {}).get('google_results_count', 0)}")
            logger.info(f"   Reddit帖子数: {loaded_data.get('statistics', {}).get('reddit_posts_count', 0)}")
        else:
            logger.error("❌ 会话数据加载失败")
            return False
        
        # 列出会话
        logger.info("📋 列出会话...")
        sessions = data_service.list_sessions(limit=5)
        logger.info(f"✅ 找到 {len(sessions)} 个会话")
        
        # 获取存储统计
        logger.info("📊 获取存储统计...")
        stats = data_service.get_storage_statistics()
        logger.info(f"✅ 存储类型: {stats.get('storage_type', 'unknown')}")
        logger.info(f"   数据库启用: {stats.get('database_enabled', False)}")
        logger.info(f"   JSON备份启用: {stats.get('json_backup_enabled', True)}")
        
        if 'json_files' in stats:
            json_stats = stats['json_files']
            logger.info(f"   JSON文件数: {json_stats.get('total_files', 0)}")
            logger.info(f"   会话文件数: {json_stats.get('session_count', 0)}")
        
        # 清理测试数据
        logger.info("🗑️ 清理测试数据...")
        if data_service.delete_session(session_id):
            logger.info("✅ 测试数据清理成功")
        else:
            logger.warning("⚠️ 测试数据清理失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_configuration():
    """测试数据库配置"""
    logger = get_logger(__name__)
    
    logger.info("🔧 测试数据库配置...")
    
    # 显示当前配置
    logger.info(f"   数据库启用: {config.ENABLE_DATABASE}")
    logger.info(f"   JSON备份启用: {config.ENABLE_JSON_BACKUP}")
    logger.info(f"   数据库已配置: {config.database_configured}")
    
    if config.DATABASE_URL:
        logger.info(f"   数据库URL: {config.DATABASE_URL[:50]}...")
    else:
        logger.info(f"   数据库主机: {config.DB_HOST}")
        logger.info(f"   数据库名称: {config.DB_NAME}")
        logger.info(f"   数据库用户: {config.DB_USER}")
    
    logger.info(f"   连接池大小: {config.DB_POOL_SIZE}")
    logger.info(f"   最大溢出: {config.DB_MAX_OVERFLOW}")
    
    # 验证配置
    errors = config.validate_config()
    if errors:
        logger.warning("⚠️ 配置验证发现问题:")
        for error in errors:
            logger.warning(f"   - {error}")
    else:
        logger.info("✅ 配置验证通过")
    
    return len(errors) == 0


def main():
    """主函数"""
    logger = get_logger(__name__)
    
    logger.info("CogBridges Search - 数据库集成测试")
    logger.info("=" * 50)
    
    success = True
    
    # 测试数据库配置
    if not test_database_configuration():
        logger.warning("⚠️ 数据库配置测试有警告，但继续进行")
    
    # 测试数据服务基本功能
    if not test_data_service_basic():
        logger.error("❌ 数据服务基本功能测试失败")
        success = False
    
    if success:
        logger.info("\n🎉 所有测试通过！")
        logger.info("\n📝 下一步:")
        logger.info("   1. 如需启用数据库，请设置环境变量 ENABLE_DATABASE=True")
        logger.info("   2. 配置数据库连接信息（DATABASE_URL 或 DB_HOST/DB_NAME/DB_USER/DB_PASSWORD）")
        logger.info("   3. 运行 python scripts/init_database.py --create-tables 创建数据库表")
        logger.info("   4. 运行 python scripts/init_database.py --migrate 迁移现有JSON数据")
    else:
        logger.error("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
