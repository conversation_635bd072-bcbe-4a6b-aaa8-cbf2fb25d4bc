"""
CogBridges Search - 配置管理模块
统一管理所有配置项，支持环境变量和默认值
"""

import os
from pathlib import Path
from typing import Optional, List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # =============================================================================
    # 基础路径配置
    # =============================================================================
    BASE_DIR = Path(__file__).parent
    DATA_DIR = BASE_DIR / os.getenv("DATA_DIR", "data")
    LOGS_DIR = BASE_DIR / os.getenv("LOGS_DIR", "data/logs")
    RESULTS_DIR = BASE_DIR / os.getenv("RESULTS_DIR", "data/results")
    
    # 确保目录存在
    DATA_DIR.mkdir(exist_ok=True)
    LOGS_DIR.mkdir(exist_ok=True)
    RESULTS_DIR.mkdir(exist_ok=True)
    
    # =============================================================================
    # Flask 应用配置
    # =============================================================================
    SECRET_KEY = os.getenv("SECRET_KEY", "cogbridges-search-secret-key-2024")
    FLASK_ENV = os.getenv("FLASK_ENV", "development")
    FLASK_DEBUG = os.getenv("FLASK_DEBUG", "True").lower() == "true"
    HOST = os.getenv("HOST", "127.0.0.1")
    PORT = int(os.getenv("PORT", "5000"))
    
    # =============================================================================
    # Google搜索配置（HTTP模式）
    # =============================================================================
    GOOGLE_SEARCH_RESULTS_COUNT = int(os.getenv("GOOGLE_SEARCH_RESULTS_COUNT", "5"))

    # Google Custom Search API 配置
    GOOGLE_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY", "")
    GOOGLE_SEARCH_ENGINE_ID = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")

    @property
    def google_search_configured(self) -> bool:
        """检查Google Custom Search API是否已配置"""
        return bool(self.GOOGLE_API_KEY and self.GOOGLE_SEARCH_ENGINE_ID)
    
    # =============================================================================
    # Reddit API 配置
    # =============================================================================
    REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID", "")
    REDDIT_CLIENT_SECRET = os.getenv("REDDIT_CLIENT_SECRET", "")
    REDDIT_USER_AGENT = os.getenv("REDDIT_USER_AGENT", "CogBridges-Search/1.0")
    REDDIT_TOP_COMMENTS_COUNT = int(os.getenv("REDDIT_TOP_COMMENTS_COUNT", "6"))
    REDDIT_API_RATE_LIMIT = int(os.getenv("REDDIT_API_RATE_LIMIT", "60"))  # 每分钟请求数
    
    # Reddit配置验证
    @property
    def reddit_configured(self) -> bool:
        """检查Reddit API是否已配置"""
        return bool(self.REDDIT_CLIENT_ID and self.REDDIT_CLIENT_SECRET)
    
    # =============================================================================
    # 用户历史数据配置
    # =============================================================================
    USER_HISTORY_COMMENTS_COUNT = int(os.getenv("USER_HISTORY_COMMENTS_COUNT", "20"))
    USER_HISTORY_POSTS_COUNT = int(os.getenv("USER_HISTORY_POSTS_COUNT", "10"))
    

    
    # =============================================================================
    # 日志配置
    # =============================================================================
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
    SAVE_DETAILED_LOGS = os.getenv("SAVE_DETAILED_LOGS", "True").lower() == "true"
    
    # =============================================================================
    # 性能配置
    # =============================================================================
    MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS", "10"))
    REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "30"))
    MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
    REQUEST_DELAY = float(os.getenv("REQUEST_DELAY", "1.0"))
    
    # =============================================================================
    # Replicate API 配置
    # =============================================================================
    REPLICATE_API_TOKEN = os.getenv("REPLICATE_API_TOKEN", "")
    REPLICATE_MODEL = os.getenv("REPLICATE_MODEL", "openai/gpt-4.1-mini")

    # Replicate配置验证
    @property
    def replicate_configured(self) -> bool:
        """检查Replicate API是否已配置"""
        return bool(self.REPLICATE_API_TOKEN)

    # =============================================================================
    # 安全配置
    # =============================================================================
    ENABLE_CORS = os.getenv("ENABLE_CORS", "True").lower() == "true"
    ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:5000,http://127.0.0.1:5000").split(",")
    
    # =============================================================================
    # 开发配置
    # =============================================================================
    DEBUG_MODE = os.getenv("DEBUG_MODE", "True").lower() == "true"
    ENABLE_PROFILING = os.getenv("ENABLE_PROFILING", "False").lower() == "true"
    TEST_MODE = os.getenv("TEST_MODE", "False").lower() == "true"
    
    # =============================================================================
    # 配置验证方法
    # =============================================================================
    def validate_config(self) -> List[str]:
        """验证配置完整性，返回错误信息列表"""
        errors = []

        # 检查必需的API配置
        if not self.reddit_configured:
            errors.append("Reddit API未配置 (REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET)")

        # 检查数值配置的合理性
        if self.GOOGLE_SEARCH_RESULTS_COUNT < 1 or self.GOOGLE_SEARCH_RESULTS_COUNT > 10:
            errors.append("Google搜索结果数量应在1-10之间")

        if self.REDDIT_TOP_COMMENTS_COUNT < 1 or self.REDDIT_TOP_COMMENTS_COUNT > 50:
            errors.append("Reddit评论数量应在1-50之间")

        if self.USER_HISTORY_COMMENTS_COUNT < 1 or self.USER_HISTORY_COMMENTS_COUNT > 100:
            errors.append("用户历史评论数量应在1-100之间")

        if self.USER_HISTORY_POSTS_COUNT < 1 or self.USER_HISTORY_POSTS_COUNT > 50:
            errors.append("用户历史帖子数量应在1-50之间")

        return errors
    
    def get_config_summary(self) -> dict:
        """获取配置摘要信息"""
        return {
            "google_search_configured": self.google_search_configured,
            "reddit_configured": self.reddit_configured,
            "search_results_count": self.GOOGLE_SEARCH_RESULTS_COUNT,
            "comments_count": self.REDDIT_TOP_COMMENTS_COUNT,
            "user_history_comments": self.USER_HISTORY_COMMENTS_COUNT,
            "user_history_posts": self.USER_HISTORY_POSTS_COUNT,
            "debug_mode": self.DEBUG_MODE,
            "test_mode": self.TEST_MODE,
            "log_level": self.LOG_LEVEL
        }

# 创建全局配置实例
config = Config()

# 配置验证
def validate_startup_config():
    """启动时验证配置"""
    errors = config.validate_config()
    if errors:
        print("⚠️ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        print("\n请检查 .env 文件或环境变量配置")
        return False
    
    print("✅ 配置验证通过")
    return True

if __name__ == "__main__":
    # 测试配置
    print("CogBridges Search - 配置测试")
    print("=" * 50)
    
    # 验证配置
    is_valid = validate_startup_config()
    
    # 显示配置摘要
    print("\n📋 配置摘要:")
    summary = config.get_config_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    print(f"\n🏠 数据目录: {config.DATA_DIR}")
    print(f"📝 日志目录: {config.LOGS_DIR}")
    print(f"💾 结果目录: {config.RESULTS_DIR}")
