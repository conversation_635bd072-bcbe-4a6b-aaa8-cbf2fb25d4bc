"""
CogBridges Search - 简化版Reddit服务
基于test_full_history_comments.py的成功逻辑，实现高效的overview获取
"""

import asyncio
import time
from typing import List, Optional, Dict, Any
from datetime import datetime
import asyncpraw
import aiohttp
from aiohttp import ClientSession
import os

from config import config
from utils.logger_utils import get_logger


class RedditService:
    """简化版Reddit服务类"""
    
    def __init__(self):
        """初始化Reddit服务"""
        self.logger = get_logger(__name__)
        
        # 检查配置
        self.configured = config.reddit_configured
        if not self.configured:
            self.logger.warning("Reddit API未配置，将使用有限功能模式")
        
        # 异步Reddit客户端（延迟初始化）
        self.async_reddit = None
        self._async_initialized = False
        
        self.logger.info("简化版Reddit服务初始化成功")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_async_reddit()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def _ensure_async_reddit(self):
        """确保异步Reddit客户端已初始化"""
        if not self.configured:
            raise ValueError("Reddit API未配置，无法使用异步功能")

        if not self._async_initialized:
            # 创建不带超时设置的session，避免兼容性问题
            session = ClientSession(trust_env=True)

            self.async_reddit = asyncpraw.Reddit(
                client_id=config.REDDIT_CLIENT_ID,
                client_secret=config.REDDIT_CLIENT_SECRET,
                user_agent=config.REDDIT_USER_AGENT,
                requestor_kwargs={"session": session}
            )
            self.async_reddit.read_only = True
            self._async_initialized = True

            self.logger.debug("异步Reddit客户端初始化完成")

        return self.async_reddit

    async def close(self):
        """关闭异步Reddit客户端和相关资源"""
        if self._async_initialized and self.async_reddit:
            try:
                await self.async_reddit.close()
                self.logger.debug("异步Reddit客户端已关闭")
            except Exception as e:
                self.logger.warning(f"关闭异步Reddit客户端时出错: {e}")
            finally:
                self.async_reddit = None
                self._async_initialized = False

    def parse_reddit_url(self, url: str) -> Optional[Dict[str, str]]:
        """解析Reddit URL，提取相关信息"""
        try:
            from urllib.parse import urlparse
            import re
            
            parsed = urlparse(url)
            path = parsed.path
            
            # 匹配帖子URL: /r/subreddit/comments/post_id/title/
            post_pattern = r'/r/([^/]+)/comments/([^/]+)'
            match = re.search(post_pattern, path)
            if match:
                return {
                    "type": "post",
                    "subreddit": match.group(1),
                    "post_id": match.group(2),
                    "url": url
                }
            
            return {"type": "unknown", "url": url}
            
        except Exception as e:
            self.logger.error(f"解析Reddit URL失败: {e}")
            return None

    async def get_post_details(self, post_url: str) -> Optional[Dict[str, Any]]:
        """获取Reddit帖子详细信息"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                url_info = self.parse_reddit_url(post_url)
                if not url_info or url_info["type"] != "post":
                    self.logger.error(f"无效的Reddit帖子URL: {post_url}")
                    return None
                
                post_id = url_info["post_id"]
                reddit = await self._ensure_async_reddit()
                
                # 使用超时包装
                submission = await asyncio.wait_for(
                    reddit.submission(id=post_id),
                    timeout=30
                )
                await asyncio.wait_for(submission.load(), timeout=30)
                
                return {
                    "id": submission.id,
                    "title": submission.title,
                    "author": submission.author.name if submission.author else "[deleted]",
                    "score": submission.score,
                    "num_comments": submission.num_comments,
                    "subreddit": submission.subreddit.display_name,
                    "url": submission.url,
                    "created_utc": submission.created_utc,
                    "selftext": getattr(submission, 'selftext', '')
                }
                
            except asyncio.TimeoutError:
                self.logger.warning(f"获取Reddit帖子超时 (尝试 {attempt + 1}/{max_retries}): {post_url}")
                if attempt == max_retries - 1:
                    self.logger.error(f"获取Reddit帖子最终失败: {post_url}")
                    return None
                await asyncio.sleep(1)  # 短暂延迟后重试
            except Exception as e:
                self.logger.error(f"获取Reddit帖子失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return None
                await asyncio.sleep(1)  # 短暂延迟后重试
        return None

    async def get_post_comments(self, post_url: str, limit: int = 6) -> List[Dict[str, Any]]:
        """获取Reddit帖子的评论"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                url_info = self.parse_reddit_url(post_url)
                if not url_info or url_info["type"] != "post":
                    self.logger.error(f"无效的Reddit帖子URL: {post_url}")
                    return []

                post_id = url_info["post_id"]
                reddit = await self._ensure_async_reddit()

                # 使用超时包装
                submission = await asyncio.wait_for(
                    reddit.submission(id=post_id),
                    timeout=30
                )
                await asyncio.wait_for(submission.load(), timeout=30)
                await asyncio.wait_for(submission.comments.replace_more(limit=3), timeout=30)

                comments = []
                comment_count = 0

                for comment in submission.comments:
                    if comment_count >= limit:
                        break

                    if hasattr(comment, 'body') and comment.body != '[deleted]':
                        try:
                            reddit_comment = {
                                "id": comment.id,
                                "body": comment.body,
                                "author": comment.author.name if comment.author else "[deleted]",
                                "score": comment.score,
                                "created_utc": comment.created_utc,
                                "parent_id": comment.parent_id,
                                "subreddit": comment.subreddit.display_name,
                                "permalink": comment.permalink
                            }
                            comments.append(reddit_comment)
                            comment_count += 1
                        except Exception as e:
                            self.logger.warning(f"解析评论失败: {e}")
                            continue

                self.logger.info(f"获取评论成功: {len(comments)} 条评论")
                return comments

            except asyncio.TimeoutError:
                self.logger.warning(f"获取Reddit评论超时 (尝试 {attempt + 1}/{max_retries}): {post_url}")
                if attempt == max_retries - 1:
                    self.logger.error(f"获取Reddit评论最终失败: {post_url}")
                    return []
                await asyncio.sleep(1)  # 短暂延迟后重试
            except Exception as e:
                self.logger.error(f"获取Reddit评论失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return []
                await asyncio.sleep(1)  # 短暂延迟后重试
        return []

    async def get_user_full_overview_history(self, username: str, max_items: int = 100) -> Dict[str, Any]:
        """获取单个用户的所有历史overview（帖子和评论）"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                reddit = await self._ensure_async_reddit()
                redditor = await reddit.redditor(username)
                
                submissions = []
                comments = []
                total_count = 0
                start_time = time.time()
                
                # 使用超时包装获取用户历史数据
                async def get_user_history():
                    # 获取所有历史overview（包括帖子和评论），按top排序
                    async for item in redditor.top(limit=None):
                        nonlocal total_count
                        total_count += 1

                        # 判断是帖子还是评论
                        if hasattr(item, 'is_self') or hasattr(item, 'title'):  # 这是帖子
                            submission_info = await self._get_submission_info(item)
                            submissions.append(submission_info)
                        else:  # 这是评论
                            comment_info = await self._get_comment_info(item)
                            # 只保留直接回复帖子的评论
                            if comment_info.get('is_reply_to_submission', False):
                                comments.append(comment_info)

                        # 设置安全限制，避免无限循环
                        if total_count >= max_items:
                            break

                await asyncio.wait_for(get_user_history(), timeout=60)
                
                end_time = time.time()
                total_time = end_time - start_time
                
                return {
                    "username": username,
                    "status": "success",
                    "total_items": total_count,
                    "submissions_count": len(submissions),
                    "comments_count": len(comments),
                    "total_time": total_time,
                    "rate_per_second": total_count/total_time if total_time > 0 else 0,
                    "submissions": submissions,
                    "comments": comments,
                    "reached_limit": total_count >= max_items
                }
                
            except asyncio.TimeoutError:
                self.logger.warning(f"获取用户 {username} overview超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    self.logger.error(f"获取用户 {username} overview最终失败")
                    return {
                        "username": username,
                        "status": "error",
                        "error": "timeout",
                        "total_items": 0,
                        "submissions_count": 0,
                        "comments_count": 0,
                        "total_time": 0,
                        "rate_per_second": 0,
                        "submissions": [],
                        "comments": [],
                        "reached_limit": False
                    }
                await asyncio.sleep(2)  # 用户数据获取失败后延迟更长时间
            except Exception as e:
                self.logger.warning(f"获取用户 {username} overview失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return {
                        "username": username,
                        "status": "error",
                        "error": str(e),
                        "total_items": 0,
                        "submissions_count": 0,
                        "comments_count": 0,
                        "total_time": 0,
                        "rate_per_second": 0,
                        "submissions": [],
                        "comments": [],
                        "reached_limit": False
                    }
                await asyncio.sleep(2)  # 用户数据获取失败后延迟更长时间
        return {
            "username": username,
            "status": "error",
            "error": "max_retries_exceeded",
            "total_items": 0,
            "submissions_count": 0,
            "comments_count": 0,
            "total_time": 0,
            "rate_per_second": 0,
            "submissions": [],
            "comments": [],
            "reached_limit": False
        }

    async def _get_submission_info(self, submission) -> Dict[str, Any]:
        """获取帖子的基本信息"""
        try:
            return {
                "score": getattr(submission, 'score', 0),
                "title": getattr(submission, 'title', 'N/A'),
                "selftext": getattr(submission, 'selftext', 'N/A'),
                "subreddit": getattr(submission, 'subreddit_name_prefixed', 'N/A'),
                "created_utc": getattr(submission, 'created_utc', 0)
            }
        except Exception as e:
            return {
                "error": f"获取帖子信息失败: {str(e)}",
                "score": 0,
                "title": "N/A",
                "selftext": "N/A",
                "subreddit": "N/A",
                "created_utc": 0
            }

    async def _get_comment_info(self, comment) -> Dict[str, Any]:
        """获取评论的基本信息"""
        try:
            # 判断是回复帖子还是评论
            parent_id = getattr(comment, 'parent_id', 'N/A')
            is_reply_to_submission = parent_id.startswith('t3_') if parent_id != 'N/A' else False
            
            return {
                "body": getattr(comment, 'body', 'N/A'),
                "score": getattr(comment, 'score', 0),
                "created_utc": getattr(comment, 'created_utc', 0),
                "subreddit": getattr(comment, 'subreddit_name_prefixed', 'N/A'),
                "is_reply_to_submission": is_reply_to_submission,
                "submission_title": getattr(comment, 'link_title', 'N/A')
            }
        except Exception as e:
            return {
                "error": f"获取评论信息失败: {str(e)}",
                "body": "N/A",
                "score": 0,
                "created_utc": 0,
                "subreddit": "N/A",
                "is_reply_to_submission": False,
                "submission_title": "N/A"
            }

    def extract_commenters(self, comments: List[Dict[str, Any]]) -> List[str]:
        """从评论列表中提取评论者用户名"""
        commenters = set()
        for comment in comments:
            if comment.get('author') and comment['author'] != "[deleted]":
                commenters.add(comment['author'])
        return list(commenters)

    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "api_configured": config.reddit_configured,
            "service_type": "simplified_reddit_service"
        } 