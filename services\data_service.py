"""
CogBridges Search - 数据存储服务
实现JSON格式的数据存储和日志记录系统
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import hashlib

from config import config
from models.search_models import SearchResult
from models.reddit_models import RedditPost, RedditComment, UserHistory
from utils.logger_utils import get_logger

# 导入数据库服务（可选）
try:
    from services.database_service import DatabaseService
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False


class DataService:
    """数据存储服务类"""
    
    def __init__(self):
        """初始化数据存储服务"""
        self.logger = get_logger(__name__)

        # 确保数据目录存在
        self.data_dir = config.DATA_DIR
        self.results_dir = config.RESULTS_DIR
        self.logs_dir = config.LOGS_DIR

        for directory in [self.data_dir, self.results_dir, self.logs_dir]:
            directory.mkdir(parents=True, exist_ok=True)

        # 初始化数据库服务（如果可用且启用）
        self.database_service = None
        if DATABASE_AVAILABLE and config.ENABLE_DATABASE:
            try:
                self.database_service = DatabaseService()
                self.logger.info("数据库服务已启用")
            except Exception as e:
                self.logger.warning(f"数据库服务初始化失败，将仅使用JSON存储: {e}")

        self.logger.info("数据存储服务初始化成功")
    
    def generate_session_id(self, query: str) -> str:
        """
        生成会话ID
        
        Args:
            query: 搜索查询
            
        Returns:
            会话ID
        """
        # 使用查询和时间戳生成唯一ID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        query_hash = hashlib.md5(query.encode('utf-8')).hexdigest()[:8]
        return f"{timestamp}_{query_hash}"
    
    def save_search_result(self, search_result: SearchResult, session_id: str = None) -> str:
        """
        保存搜索结果
        
        Args:
            search_result: 搜索结果对象
            session_id: 会话ID（可选）
            
        Returns:
            保存的文件路径
        """
        if not session_id:
            session_id = self.generate_session_id(search_result.query.query)
        
        # 创建文件名
        filename = f"search_result_{session_id}.json"
        filepath = self.results_dir / filename
        
        try:
            # 保存为JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(search_result.to_dict(), f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"搜索结果已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")
            raise
    
    def save_reddit_data(self, reddit_data: Dict[str, Any], session_id: str) -> str:
        """
        保存Reddit数据
        
        Args:
            reddit_data: Reddit数据字典
            session_id: 会话ID
            
        Returns:
            保存的文件路径
        """
        filename = f"reddit_data_{session_id}.json"
        filepath = self.results_dir / filename
        
        try:
            # 转换数据为可序列化格式
            serializable_data = self._make_serializable(reddit_data)
            
            # 保存为JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Reddit数据已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"保存Reddit数据失败: {e}")
            raise
    
    def save_complete_session(
        self,
        session_id: str,
        data_to_save: Dict[str, Any]
    ) -> str:
        """
        保存完整的会话数据（支持双存储）

        Args:
            session_id: 会话ID
            data_to_save: 要保存的数据字典

        Returns:
            保存的文件路径
        """
        # 序列化数据
        serializable_data = self._make_serializable(data_to_save)

        # 保存到数据库（如果启用）
        if self.database_service and self.database_service.is_available():
            try:
                # 从数据中提取搜索结果
                search_result_data = serializable_data.get('search_result', {})
                if search_result_data:
                    # 重构SearchResult对象
                    search_result = self._reconstruct_search_result(search_result_data)
                    reddit_data = serializable_data.get('reddit_data', {})

                    # 保存到数据库
                    db_success = self.database_service.save_search_session(
                        session_id=session_id,
                        search_result=search_result,
                        reddit_data=reddit_data,
                        raw_data=serializable_data
                    )

                    if db_success:
                        self.logger.info(f"会话数据已保存到数据库: {session_id}")
                    else:
                        self.logger.warning(f"数据库保存失败，仅保存JSON: {session_id}")

            except Exception as e:
                self.logger.warning(f"数据库保存失败: {e}，仅保存JSON")

        # 保存到JSON文件（如果启用或数据库保存失败）
        json_filepath = None
        if config.ENABLE_JSON_BACKUP:
            try:
                filename = f"complete_session_{session_id}.json"
                filepath = self.results_dir / filename

                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(serializable_data, f, ensure_ascii=False, indent=2)

                json_filepath = str(filepath)
                self.logger.info(f"完整会话数据已保存到JSON: {filepath}")

            except Exception as e:
                self.logger.error(f"保存JSON文件失败: {e}")
                if not (self.database_service and self.database_service.is_available()):
                    # 如果数据库也不可用，则抛出异常
                    raise

        return json_filepath or f"database:{session_id}"

    def _reconstruct_search_result(self, search_result_data: Dict[str, Any]) -> SearchResult:
        """
        从字典数据重构SearchResult对象

        Args:
            search_result_data: 搜索结果数据字典

        Returns:
            SearchResult对象
        """
        from models.search_models import SearchQuery, GoogleSearchResult as GoogleSearchResultModel

        # 重构SearchQuery
        query_data = search_result_data.get('query', {})
        search_query = SearchQuery(
            query=query_data.get('query', ''),
            timestamp=datetime.fromisoformat(query_data.get('timestamp', datetime.now().isoformat())),
            search_type=query_data.get('search_type', 'reddit'),
            max_results=query_data.get('max_results', 5),
            site_filter=query_data.get('site_filter', 'site:reddit.com')
        )

        # 重构SearchResult
        search_result = SearchResult(
            query=search_query,
            success=search_result_data.get('success', True),
            error_message=search_result_data.get('error_message')
        )

        # 重构Google搜索结果
        if 'google_results' in search_result_data:
            search_result.google_results = []
            for result_data in search_result_data['google_results']:
                google_result = GoogleSearchResultModel(
                    title=result_data.get('title', ''),
                    url=result_data.get('url', ''),
                    snippet=result_data.get('snippet', ''),
                    display_url=result_data.get('display_url', ''),
                    rank=result_data.get('rank', 0)
                )
                search_result.google_results.append(google_result)

        return search_result
    
    def load_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        加载会话数据（支持从数据库或JSON文件加载）

        Args:
            session_id: 会话ID

        Returns:
            会话数据字典
        """
        # 优先从数据库加载
        if self.database_service and self.database_service.is_available():
            try:
                data = self.database_service.load_search_session(session_id)
                if data:
                    self.logger.info(f"从数据库加载会话数据成功: {session_id}")
                    return data
                else:
                    self.logger.info(f"数据库中未找到会话，尝试从JSON加载: {session_id}")
            except Exception as e:
                self.logger.warning(f"从数据库加载失败: {e}，尝试从JSON加载")

        # 从JSON文件加载
        filename = f"complete_session_{session_id}.json"
        filepath = self.results_dir / filename

        try:
            if not filepath.exists():
                self.logger.warning(f"会话文件不存在: {filepath}")
                return None

            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.logger.info(f"从JSON文件加载会话数据成功: {session_id}")
            return data

        except Exception as e:
            self.logger.error(f"加载会话数据失败: {e}")
            return None
    
    def list_sessions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        列出最近的会话（支持从数据库和JSON文件）

        Args:
            limit: 返回数量限制

        Returns:
            会话信息列表
        """
        sessions = []

        # 优先从数据库获取
        if self.database_service and self.database_service.is_available():
            try:
                db_sessions = self.database_service.list_search_sessions(limit=limit)
                for session_data in db_sessions:
                    session_info = {
                        "session_id": session_data.get("id"),
                        "timestamp": session_data.get("timestamp", ""),
                        "query": session_data.get("query", ""),
                        "source": "database",
                        "google_results_count": session_data.get("google_results_count", 0),
                        "reddit_posts_count": session_data.get("reddit_posts_count", 0),
                        "reddit_comments_count": session_data.get("reddit_comments_count", 0),
                        "success": session_data.get("success", True),
                        "created_at": session_data.get("created_at", ""),
                        "updated_at": session_data.get("updated_at", "")
                    }
                    sessions.append(session_info)

                self.logger.info(f"从数据库获取到 {len(sessions)} 个会话")

                # 如果数据库中的会话数量已满足要求，直接返回
                if len(sessions) >= limit:
                    return sessions[:limit]

            except Exception as e:
                self.logger.warning(f"从数据库获取会话失败: {e}，尝试从JSON文件获取")

        # 从JSON文件获取（补充或备用）
        try:
            remaining_limit = limit - len(sessions)
            if remaining_limit > 0:
                # 查找所有会话文件
                pattern = "complete_session_*.json"
                session_files = list(self.results_dir.glob(pattern))

                # 按修改时间排序
                session_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

                # 获取已有会话的ID集合，避免重复
                existing_ids = {s["session_id"] for s in sessions}

                for filepath in session_files:
                    if len(sessions) >= limit:
                        break

                    try:
                        # 从文件名提取会话ID
                        session_id = filepath.stem.replace("complete_session_", "")

                        # 跳过已存在的会话
                        if session_id in existing_ids:
                            continue

                        # 获取文件信息
                        stat = filepath.stat()

                        # 尝试读取基本信息
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        session_info = {
                            "session_id": session_id,
                            "timestamp": data.get("timestamp", ""),
                            "query": data.get("search_result", {}).get("query", {}).get("query", ""),
                            "source": "json_file",
                            "file_size": stat.st_size,
                            "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            "statistics": data.get("statistics", {})
                        }

                        sessions.append(session_info)

                    except Exception as e:
                        self.logger.warning(f"读取会话文件失败 {filepath}: {e}")
                        continue

                self.logger.info(f"从JSON文件补充获取到 {len(sessions) - len([s for s in sessions if s.get('source') == 'database'])} 个会话")

            return sessions

        except Exception as e:
            self.logger.error(f"列出会话失败: {e}")
            return sessions  # 返回已获取的会话（如果有的话）
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除会话数据（支持从数据库和JSON文件删除）

        Args:
            session_id: 会话ID

        Returns:
            删除是否成功
        """
        success = False

        # 从数据库删除
        if self.database_service and self.database_service.is_available():
            try:
                db_success = self.database_service.delete_search_session(session_id)
                if db_success:
                    self.logger.info(f"从数据库删除会话成功: {session_id}")
                    success = True
                else:
                    self.logger.warning(f"数据库中未找到会话: {session_id}")
            except Exception as e:
                self.logger.warning(f"从数据库删除会话失败: {e}")

        # 删除JSON文件
        try:
            patterns = [
                f"complete_session_{session_id}.json",
                f"search_result_{session_id}.json",
                f"reddit_data_{session_id}.json"
            ]

            deleted_count = 0
            for pattern in patterns:
                filepath = self.results_dir / pattern
                if filepath.exists():
                    filepath.unlink()
                    deleted_count += 1

            if deleted_count > 0:
                self.logger.info(f"JSON文件删除成功: {session_id} ({deleted_count} 个文件)")
                success = True
            elif not success:
                self.logger.warning(f"未找到会话数据: {session_id}")

        except Exception as e:
            self.logger.error(f"删除JSON文件失败: {e}")
            if not success:
                return False

        return success
    
    def _make_serializable(self, data: Any) -> Any:
        """
        将数据转换为可序列化格式
        
        Args:
            data: 原始数据
            
        Returns:
            可序列化的数据
        """
        if isinstance(data, dict):
            return {key: self._make_serializable(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        elif hasattr(data, 'to_dict'):
            return data.to_dict()
        elif isinstance(data, (datetime,)):
            return data.isoformat()
        else:
            return data
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """
        获取存储统计信息（包括数据库和JSON文件）

        Returns:
            存储统计信息字典
        """
        statistics = {
            "storage_type": "hybrid" if (self.database_service and self.database_service.is_available()) else "json_only",
            "database_enabled": config.ENABLE_DATABASE,
            "json_backup_enabled": config.ENABLE_JSON_BACKUP
        }

        # 获取数据库统计
        if self.database_service and self.database_service.is_available():
            try:
                db_stats = self.database_service.get_database_statistics()
                statistics["database"] = db_stats
            except Exception as e:
                self.logger.warning(f"获取数据库统计失败: {e}")
                statistics["database"] = {"error": str(e)}

        # 获取JSON文件统计
        try:
            total_files = 0
            total_size = 0

            for filepath in self.results_dir.rglob("*"):
                if filepath.is_file():
                    total_files += 1
                    total_size += filepath.stat().st_size

            # 统计会话数量
            session_files = list(self.results_dir.glob("complete_session_*.json"))

            statistics["json_files"] = {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "session_count": len(session_files),
                "data_directory": str(self.data_dir),
                "results_directory": str(self.results_dir),
                "logs_directory": str(self.logs_dir)
            }

        except Exception as e:
            self.logger.error(f"获取JSON文件统计失败: {e}")
            statistics["json_files"] = {"error": str(e)}

        return statistics

    def migrate_json_to_database(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        将JSON文件数据迁移到数据库

        Args:
            session_id: 指定会话ID（可选），如果不指定则迁移所有

        Returns:
            迁移结果统计
        """
        if not (self.database_service and self.database_service.is_available()):
            return {"error": "数据库服务不可用"}

        try:
            results = {
                "total_files": 0,
                "migrated": 0,
                "skipped": 0,
                "failed": 0,
                "errors": []
            }

            # 确定要迁移的文件
            if session_id:
                pattern = f"complete_session_{session_id}.json"
                files = [self.results_dir / pattern] if (self.results_dir / pattern).exists() else []
            else:
                files = list(self.results_dir.glob("complete_session_*.json"))

            results["total_files"] = len(files)

            for filepath in files:
                try:
                    success = self.database_service.migrate_json_to_database(str(filepath))
                    if success:
                        results["migrated"] += 1
                    else:
                        results["skipped"] += 1
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"{filepath.name}: {str(e)}")
                    self.logger.error(f"迁移文件失败 {filepath}: {e}")

            self.logger.info(f"迁移完成: {results}")
            return results

        except Exception as e:
            self.logger.error(f"迁移过程失败: {e}")
            return {"error": str(e)}


if __name__ == "__main__":
    # 测试数据存储服务
    print("CogBridges Search - 数据存储服务测试")
    print("=" * 50)
    
    try:
        # 创建数据服务
        data_service = DataService()
        
        # 测试生成会话ID
        print("🆔 测试会话ID生成...")
        test_query = "python programming"
        session_id = data_service.generate_session_id(test_query)
        print(f"生成的会话ID: {session_id}")
        
        # 测试保存测试数据
        print("\n💾 测试数据保存...")
        
        # 模拟保存会话数据
        from models.search_models import SearchQuery, SearchResult
        
        search_query = SearchQuery(query=test_query)
        search_result = SearchResult(query=search_query, success=True)
        
        test_data = {
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "search_result": search_result.to_dict(),
            "metadata": {"test_run": True}
        }
        
        filepath = data_service.save_complete_session(
            session_id, test_data
        )
        print(f"✅ 数据保存成功: {filepath}")
        
        # 测试加载数据
        print("\n📂 测试数据加载...")
        loaded_data = data_service.load_session_data(session_id)
        if loaded_data:
            print("✅ 数据加载成功")
            print(f"   查询: {loaded_data.get('search_result', {}).get('query', {}).get('query', 'N/A')}")
        else:
            print("❌ 数据加载失败")
        
        # 测试列出会话
        print("\n📋 测试会话列表...")
        sessions = data_service.list_sessions(limit=5)
        print(f"找到 {len(sessions)} 个会话")
        
        # 显示存储统计
        print("\n📊 存储统计:")
        stats = data_service.get_storage_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 清理测试数据
        print(f"\n🗑️ 清理测试数据...")
        if data_service.delete_session(session_id):
            print("✅ 测试数据清理成功")
        else:
            print("⚠️ 测试数据清理失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
