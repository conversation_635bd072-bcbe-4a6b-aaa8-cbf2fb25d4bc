# CogBridges Search - 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# 基础配置
# =============================================================================
FLASK_ENV=development
FLASK_DEBUG=True
HOST=127.0.0.1
PORT=5000
SECRET_KEY=cogbridges-search-secret-key-2024

# =============================================================================
# 数据库配置
# =============================================================================
# 启用数据库存储（设置为True以启用PostgreSQL数据库）
ENABLE_DATABASE=False

# JSON文件备份（即使启用数据库也保留JSON备份）
ENABLE_JSON_BACKUP=True

# Render部署时使用（Render会自动提供此环境变量）
# DATABASE_URL=postgresql://user:password@host:port/database

# 本地开发数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cogbridges_db
DB_USER=postgres
DB_PASSWORD=your_password_here

# 数据库连接池配置
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30

# =============================================================================
# Google搜索配置
# =============================================================================
# Google Custom Search API（可选）
GOOGLE_SEARCH_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
GOOGLE_SEARCH_RESULTS_COUNT=5

# =============================================================================
# Reddit API 配置
# =============================================================================
# Reddit API 凭据（必需）
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USER_AGENT=CogBridges-Search/1.0

# Reddit API 参数
REDDIT_TOP_COMMENTS_COUNT=6
REDDIT_API_RATE_LIMIT=60

# =============================================================================
# 用户历史数据配置
# =============================================================================
USER_HISTORY_COMMENTS_COUNT=20
USER_HISTORY_POSTS_COUNT=10

# =============================================================================
# LLM服务配置
# =============================================================================
# Replicate API（用于LLM分析功能）
REPLICATE_API_TOKEN=your_replicate_token_here
REPLICATE_MODEL=openai/gpt-4.1-mini

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
SAVE_DETAILED_LOGS=True

# =============================================================================
# 性能配置
# =============================================================================
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
MAX_RETRIES=3
REQUEST_DELAY=1.0

# =============================================================================
# 安全配置
# =============================================================================
ENABLE_CORS=True
ALLOWED_ORIGINS=http://localhost:5000,http://127.0.0.1:5000

# =============================================================================
# 开发配置
# =============================================================================
DEBUG_MODE=True
ENABLE_PROFILING=False
TEST_MODE=False

# =============================================================================
# 数据目录配置（可选）
# =============================================================================
# DATA_DIR=data
# LOGS_DIR=data/logs
# RESULTS_DIR=data/results
